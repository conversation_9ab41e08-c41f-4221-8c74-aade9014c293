import sys, os
from pathlib import Path
from playwright.sync_api import sync_playwright

BASE_URL = os.environ.get("TARGET_URL", "https://openai-proxy.int.prod-southcentralus-hpe-3.dev.openai.org/")
USER_DATA_DIR = os.environ.get("USER_DATA_DIR", str(Path.cwd() / ".oai_auth_profile"))  # 持久化用户目录
COOKIE_NAME = os.environ.get("COOKIE_NAME", "_oauth2_proxy")
COOKIE_DOMAIN_SUFFIX = os.environ.get("COOKIE_DOMAIN_SUFFIX", ".dev.openai.org")  # 你的域后缀

def pick_cookie(cookies):
    # 从所有 Cookie 里挑：名字匹配，且域名落在 .dev.openai.org（你可按需修改）
    # 按过期时间/创建时间最后的优先
    candidates = [c for c in cookies if c.get("name") == COOKIE_NAME and c.get("domain","").endswith(COOKIE_DOMAIN_SUFFIX)]
    if not candidates:
        return None
    # 选择 expires 最大的
    candidates.sort(key=lambda c: c.get("expires") or 0, reverse=True)
    return candidates[0]

def main():
    with sync_playwright() as p:
        context = p.chromium.launch_persistent_context(
            USER_DATA_DIR,
            headless=False,  # 首次登录建议 --login（有头）
            args=[],
        )
        page = context.new_page()

        # 访问目标域，会自动 302 去登录；首次运行你手动完成登录和 MFA
        page.goto(BASE_URL, wait_until="networkidle")

        # 如果是登录模式，给足够时间完成 MFA/同意等（也可按需调大/按提示继续）
        print(f"[info] 已打开浏览器，请完成登录。完成后按回车继续抓取 Cookie…", file=sys.stderr)
        try:
            input()
        except KeyboardInterrupt:
            context.close(); return

        # 再刷一下以确保代理把站点 Cookie 写好
        page.goto(BASE_URL, wait_until="networkidle")

        # 读取 Cookie
        cookies = context.cookies()  # 所有域的 Cookie
        target = pick_cookie(cookies)
        context.close()

        if not target:
            print("[error] 没找到目标 Cookie（可能还没登录、Cookie 域不匹配，或代理没有设置 Cookie）。", file=sys.stderr)
            sys.exit(2)

        # 直接只打印值，方便脚本对接
        print(target.get("value"))

if __name__ == "__main__":
    main()
